import request from '@/utils/request'

// 产品下拉框
export function productList(data) {
  return request({
    url: '/product/list',
    method: 'get',
    params: data,
  })
}

// 部门下拉框
export function deptSelect(query) {
  return request({
    url: '/common-biz/deptSelect',
    method: 'get',
    params: query
  })
}

//部门下拉框数据（超级管理员、技术总监、项目经理可查看全部组的数据，其他只能查看本组数据）
export function deptAuthSelect(query) {
  return request({
    url: '/common-biz/deptAuthSelect',
    method: 'get',
    params: query
  })
}
