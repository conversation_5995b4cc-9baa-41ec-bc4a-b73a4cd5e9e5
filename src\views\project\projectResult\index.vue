<template>
  <div class="app-container">
    <!-- 查询条件表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="query-form">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option v-for="item in dictData.status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="负责项目经理" prop="projectManagers">
        <el-select v-model="queryParams.projectManagers" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.projectManagers" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="项目/任务" prop="projectTaskName">
        <el-input v-model="queryParams.projectTaskName" placeholder="请输入项目/任务名称" clearable style="width: 200px" />
      </el-form-item>

      <el-form-item label="成果编码" prop="resultCode">
        <el-input v-model="queryParams.resultCode" placeholder="请输入成果编码" clearable style="width: 180px" />
      </el-form-item>

      <el-form-item label="完成时间" prop="completionTimeRange">
        <el-date-picker
          v-model="queryParams.completionTimeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="请选择开始时间"
          end-placeholder="请选择结束时间"
          style="width: 350px">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="所属业务大类" prop="businessCategoryMajor">
        <el-select v-model="queryParams.businessCategoryMajor" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.businessMajor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="所属业务小类" prop="businessCategoryMinor">
        <el-select v-model="queryParams.businessCategoryMinor" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.businessMinor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>

      <el-form-item>
        <span style="color: #999;">双击可查看成果关联的需求</span>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:projectResult:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="handleArchive" :disabled="selectedRows.length === 0" v-hasPermi="['system:projectResult:archive']">归档</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-message"
          size="mini"
          :disabled="selectedRows.length === 0"
          v-hasPermi="['system:projectResult:sendEmail']"
          @click="handleSendEmail">
          发送邮件
        </el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      @row-dblclick="handleRowDblclick"
      height="calc(100vh - 400px)"
      stripe
      border>

      <el-table-column type="selection" width="55" align="center" />

      <!-- 1. 序号 -->
      <el-table-column label="序号" type="index" width="60" align="center" />

      <!-- 2. 所属业务大类 -->
      <el-table-column label="所属业务大类" align="center" prop="businessCategoryMajor" width="120" sortable="custom">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMajor, dict.type.project_outcome_business_category_major) }}</span>
        </template>
      </el-table-column>

      <!-- 3. 所属业务小类 -->
      <el-table-column label="所属业务小类" align="center" prop="businessCategoryMinor" width="120" sortable="custom">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMinor, dict.type.project_outcome_business_category_minor) }}</span>
        </template>
      </el-table-column>

      <!-- 4. 成果编码 -->
      <el-table-column label="成果编码" align="center" prop="resultCode" width="180" show-overflow-tooltip sortable="custom" />

      <!-- 5. 项目/任务名称 -->
      <el-table-column label="项目/任务名称" align="center" prop="projectTaskName" min-width="150" show-overflow-tooltip />

      <!-- 6. 状态 -->
      <el-table-column label="状态" align="center" prop="status" width="80" sortable="custom">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="mini">
            {{ getDictLabel(scope.row.status, dict.type.project_outcome_status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 7. 需求个数 -->
      <el-table-column label="需求个数" align="center" prop="storyCount" width="100" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.storyCount || 0 }}</span>
        </template>
      </el-table-column>

      <!-- 8. 项目里程碑 -->
      <el-table-column label="项目里程碑" align="center" prop="milestoneDisplay" min-width="200">
        <template slot-scope="scope">
          <div class="milestone-content" v-html="scope.row.milestoneDisplay"></div>
        </template>
      </el-table-column>

      <!-- 9. 任务说明/进度 -->
      <el-table-column label="任务说明/进度" align="center" prop="progressDisplay" min-width="200">
        <template slot-scope="scope">
          <div class="progress-content" v-html="scope.row.progressDisplay"></div>
        </template>
      </el-table-column>

      <!-- 10. 备注 -->
      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.remark || '-' }}</span>
        </template>
      </el-table-column>

      <!-- 11. 负责项目经理 -->
      <el-table-column label="负责项目经理" align="center" prop="projectManagers" min-width="180" show-overflow-tooltip sortable="custom">
        <template slot-scope="scope">
          <span>{{ convertProjectManagerIdsToNames(scope.row.projectManagers) }}</span>
        </template>
      </el-table-column>

      <!-- 12. 更新时间 -->
      <el-table-column label="更新时间" align="center" prop="updateTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>

      <!-- 13. 完成时间 -->
      <el-table-column label="完成时间" align="center" prop="completionTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.completionTime) }}</span>
        </template>
      </el-table-column>

      <!-- 14. 创建人 -->
      <el-table-column label="创建人" align="center" prop="createBy" min-width="100" sortable="custom" />

      <!-- 操作列 -->
      <el-table-column label="操作" align="center" width="180" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)" v-hasPermi="['system:projectResult:edit']">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleSync(scope.row)" v-hasPermi="['system:projectResult:sync']">同步</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)" v-hasPermi="['system:projectResult:query']">查看详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #f56c6c;" v-hasPermi="['system:projectResult:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增编辑弹窗 -->
    <addEditDialog
      :dialogVisible.sync="addEditDialog.show"
      :dialogTitle="addEditDialog.title"
      :dialogData="addEditDialog.data"
      @callback="handleQuery">
    </addEditDialog>

    <!-- 详情弹窗 -->
    <detailDialog
      :dialogVisible.sync="detailDialog.show"
      :dialogTitle="detailDialog.title"
      :recordId="detailDialog.recordId">
    </detailDialog>

    <!-- 需求背景详情弹窗 -->
    <el-dialog
      title="需求背景详情"
      :visible.sync="requirementDialog.show"
      width="70%"
      :close-on-click-modal="false"
      append-to-body>
      <div class="requirement-detail-header">
        <h4>{{ requirementDialog.projectName }}</h4>
      </div>
      <div class="requirement-detail-content">
        <div class="requirement-text-content" v-html="requirementDialog.content"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="requirementDialog.show = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 邮件预览弹窗 -->
    <EmailPreviewDialog
      :visible.sync="emailPreviewDialog.show"
      :selectedProjects="selectedRowsWithIndex"
      :dictData="dict.type"
      @sent="onEmailSent"
      @closed="onEmailDialogClosed"
    />
  </div>
</template>

<script>
// 导入API接口
import {
  projectResultList,
  projectResultDelete,
  projectResultSync,
  projectResultArchive
} from "@/api/project/projectResult"
import addEditDialog from "./components/addEditDialog.vue"
import detailDialog from "./components/detailDialog.vue"
import EmailPreviewDialog from "./components/EmailPreviewDialog.vue"
import { parseTime } from "@/utils/ruoyi"

export default {
  name: "ProjectResult",
  dicts: [
    'project_outcome_status',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_dev_dept',
    'project_outcome_test_dept'
  ],
  components: {
    addEditDialog,
    detailDialog,
    EmailPreviewDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: "",
        projectManagers: null,
        projectTaskName: "",
        resultCode: "",
        businessCategoryMajor: null,
        businessCategoryMinor: null,
        archiveFlag: null,
        completionTimeRange: []
      },
      // 弹窗配置
      addEditDialog: {
        show: false,
        title: '新增',
        data: {}
      },
      // 详情弹窗配置
      detailDialog: {
        show: false,
        title: '项目成果详情',
        recordId: null
      },
      // 需求背景详情弹窗配置
      requirementDialog: {
        show: false,
        content: '',
        projectName: ''
      },
      // 邮件预览弹窗配置
      emailPreviewDialog: {
        show: false
      },
      // 选中的行数据
      selectedRows: []

    }
  },
  computed: {
    selectedRowsWithIndex() {
      return this.selectedRows.map((row, index) => ({
        ...row,
        index: index + 1
      }))
    },
    // 统一获取字典数据
    dictData() {
      return {
        types: this.dict.type.project_outcome_types || [],
        status: this.dict.type.project_outcome_status || [],
        priority: this.dict.type.project_outcome_priority_level || [],
        businessMajor: this.dict.type.project_outcome_business_category_major || [],
        businessMinor: this.dict.type.project_outcome_business_category_minor || [],
        projectManagers: this.dict.type.project_outcome_project_manager || [],
        devDepts: this.dict.type.project_outcome_dev_dept || [],
        testDepts: this.dict.type.project_outcome_test_dept || []
      }
    }
  },
  async created() {
    // 先加载业务类型选项，然后获取列表数据
    await this.loadOptions()
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true

      // 处理时间范围参数和分页参数
      let params = { ...this.queryParams }
      if (this.queryParams.completionTimeRange && this.queryParams.completionTimeRange.length !== 0) {
        params.completionTimeStart = this.queryParams.completionTimeRange[0]
        params.completionTimeEnd = this.queryParams.completionTimeRange[1]
      }
      delete params.completionTimeRange

      // 清理空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })

      // API调用
      projectResultList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          // 处理数据，添加拼接字段
          this.tableData = (res.rows || []).map(item => {
            return {
              ...item,
              milestoneDisplay: this.formatMilestone(item),
              progressDisplay: this.formatProgress(item),
              stakeholderDisplay: this.formatStakeholders(item),
              manpowerDisplay: this.formatManpower(item),
              workloadDisplay: this.formatWorkload(item)
            }
          })
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('接口调用失败')
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },

    /** 新增 */
    handleAdd() {
      this.addEditDialog.show = true
      this.addEditDialog.title = '新增项目成果'
      this.addEditDialog.data = {}
    },

    /** 编辑 */
    handleEdit(row) {
      this.addEditDialog.show = true
      this.addEditDialog.title = '编辑项目成果'
      this.addEditDialog.data = { ...row }
    },

    /** 同步 */
    handleSync(row) {
      this.$confirm('同步后，项目里程碑、任务说明/进度、干系人都会被覆盖掉，是否确认？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectResultSync({ id: row.id }).then(res => {
          if (res.code === 200) {
            this.$message.success('同步成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '同步失败')
          }
        }).catch(() => {
          this.$message.error('同步失败')
        })
      }).catch(() => {
        this.$message.info('已取消同步')
      })
    },

    /** 查看详情 */
    handleViewDetail(row) {
      this.detailDialog.show = true
      this.detailDialog.title = `项目成果详情：${row.projectTaskName}`
      this.detailDialog.recordId = row.id
    },

    /** 显示需求背景详情 */
    showRequirementDetail(content, projectName) {
      this.requirementDialog.show = true
      this.requirementDialog.content = content || '暂无需求背景'
      this.requirementDialog.projectName = projectName || '项目成果'
    },

    /** 删除 */
    handleDelete(row) {
      this.$confirm('删除会将需求解除关联，清除所有数据，是否确认？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectResultDelete(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    /** 排序 */
    handleSortChange({ prop, order }) {
      if (order === null) {
        // 取消排序时清空排序参数
        this.queryParams.orderByColumn = null
        this.queryParams.isAsc = null
      } else {
        // 设置排序参数
        this.queryParams.orderByColumn = prop
        // Element UI 的 order 值为 'ascending' 或 'descending'，需要转换为 'asc' 或 'desc'
        this.queryParams.isAsc = order === 'ascending' ? 'asc' : 'desc'
      }
      this.handleQuery()
    },

    /** 表格选择变化 */
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    /** 归档 */
    handleArchive() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要归档的项目')
        return
      }

      const projectNames = this.selectedRows.map(row => row.projectTaskName).join('、')
      this.$confirm(`确认归档以下项目吗？\n${projectNames}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.performArchive()
      }).catch(() => {
        this.$message.info('已取消归档')
      })
    },

    /** 执行归档操作 */
    async performArchive() {
      const loading = this.$loading({
        lock: true,
        text: '归档中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        let successCount = 0
        let failCount = 0

        for (const row of this.selectedRows) {
          try {
            const res = await projectResultArchive(row.id)
            if (res.code === 200) {
              successCount++
            } else {
              failCount++
              console.error(`归档项目 ${row.projectTaskName} 失败:`, res.msg)
            }
          } catch (error) {
            failCount++
            console.error(`归档项目 ${row.projectTaskName} 失败:`, error)
          }
        }

        loading.close()

        if (failCount === 0) {
          this.$message.success(`成功归档 ${successCount} 个项目`)
        } else if (successCount === 0) {
          this.$message.error('归档失败')
        } else {
          this.$message.warning(`成功归档 ${successCount} 个项目，失败 ${failCount} 个项目`)
        }

        // 刷新列表
        this.getList()
        // 清空选择
        this.selectedRows = []
      } catch (error) {
        loading.close()
        this.$message.error('归档操作失败')
        console.error('归档操作失败:', error)
      }
    },

    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '1': 'info',
        '2': 'warning',
        '3': 'success',
        '4': 'danger'
      }
      return typeMap[status] || 'info'
    },

    /** 获取优先级类型 */
    getPriorityType(priority) {
      const typeMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'info'
      }
      return typeMap[priority] || 'info'
    },

    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },



    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },

    /** 格式化日期 */
    formatDate(date) {
      if (!date) return ''
      return parseTime(date, '{y}-{m}-{d}')
    },

    /** 格式化项目里程碑 */
    formatMilestone(row) {
      const milestones = []
      if (row.milestoneRequirements) {
        milestones.push(`完成评审：${this.formatDate(row.milestoneRequirements)}`)
      }
      if (row.milestoneDevelopment) {
        milestones.push(`完成开发：${this.formatDate(row.milestoneDevelopment)}`)
      }
      if (row.milestoneTest) {
        milestones.push(`完成测试验收：${this.formatDate(row.milestoneTest)}`)
      }
      if (row.milestoneOnline) {
        milestones.push(`完成上线：${this.formatDate(row.milestoneOnline)}`)
      }
      return milestones.join('<br/>')
    },

    /** 格式化任务说明/进度 */
    formatProgress(row) {
      const progress = []
      if (row.requirementsProgress !== null && row.requirementsProgress !== undefined) {
        progress.push(`需求评审：${row.requirementsProgress}%`)
      }
      if (row.developmentProgress !== null && row.developmentProgress !== undefined) {
        progress.push(`开发进度：${row.developmentProgress}%`)
      }
      if (row.testProgress !== null && row.testProgress !== undefined) {
        progress.push(`测试验收进度：${row.testProgress}%`)
      }
      return progress.join('<br/>')
    },

    /** 格式化干系人 */
    formatStakeholders(row) {
      const stakeholders = []
      if (row.productManagers) {
        stakeholders.push(`产品：${row.productManagers}`)
      }
      if (row.devTeams) {
        const devTeamNames = this.convertDeptIdsToNames(row.devTeams, 'dev')
        stakeholders.push(`开发：${devTeamNames}`)
      }
      if (row.testTeams) {
        const testTeamNames = this.convertDeptIdsToNames(row.testTeams, 'test')
        stakeholders.push(`测试：${testTeamNames}`)
      }
      return stakeholders.join('<br/>')
    },

    /** 格式化投入人力 */
    formatManpower(row) {
      const manpower = []
      if (row.devManpower) {
        manpower.push(`开发：${row.devManpower}人`)
      }
      if (row.testManpower) {
        manpower.push(`测试：${row.testManpower}人`)
      }
      return manpower.join('<br/>')
    },

    /** 格式化工作量（人日） */
    formatWorkload(row) {
      const workload = []
      if (row.devWorkload) {
        workload.push(`开发：${row.devWorkload}人日`)
      }
      if (row.testWorkload) {
        workload.push(`测试：${row.testWorkload}人日`)
      }
      return workload.join('<br/>')
    },

    /** 将部门ID转换为部门名称 */
    convertDeptIdsToNames(deptIds, type) {
      if (!deptIds) return ''
      const ids = deptIds.split(',')
      const deptDict = type === 'dev' ? this.dictData.devDepts : this.dictData.testDepts
      
      // 使用dictData确保有默认空数组，安全访问
      if (!deptDict || !Array.isArray(deptDict) || deptDict.length === 0) {
        return deptIds
      }

      const names = ids.map(id => {
        const dept = deptDict.find(item => item.value === id.trim())
        return dept ? dept.label : id
      })
      return names.join('、')
    },

    /** 将项目经理ID转换为项目经理名称 */
    convertProjectManagerIdsToNames(projectManagers) {
      if (!projectManagers) return ''
      const ids = projectManagers.split(',')
      const managerDict = this.dictData.projectManagers
      
      // 使用dictData确保有默认空数组，安全访问
      if (!managerDict || !Array.isArray(managerDict) || managerDict.length === 0) {
        return projectManagers
      }

      const names = ids.map(id => {
        const manager = managerDict.find(item => item.value === id.trim())
        return manager ? manager.label : id
      })
      return names.join('、')
    },
    /** 发送邮件 */
    handleSendEmail() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要发送邮件的项目成果')
        return
      }
      // 显示邮件预览弹窗
      this.emailPreviewDialog.show = true
    },
    /** 邮件发送成功回调 */
    onEmailSent() {
      this.selectedRows = []
      this.safeRefreshList()
    },

    /** 邮件弹窗关闭回调 */
    onEmailDialogClosed() {
      // 弹窗关闭时重置状态
      this.emailPreviewDialog.show = false
    },

    /** 双击行跳转到需求页面 */
    handleRowDblclick(row) {
      if (!row.resultCode) {
        this.$message.warning('该项目成果无成果编码，无法跳转')
        return
      }
      // 跳转到需求页面
      this.$router.push({
        path: '/manage/demand',
        query: {
          resultCode: row.resultCode,
          highlight: 'true'
        }
      })
    }

  }
}
</script>

<style scoped>
.query-form {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.milestone-content {
  line-height: 1.5;
}

.progress-content {
  text-align: left;
}

/* 表格单元格内容样式 */
.el-table .cell {
  line-height: 1.4;
}

/* 操作按钮样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

/* 需求背景可点击文本样式 */
.requirement-text-clickable {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  font-size: 13px;
  cursor: pointer;
  color: #606266;
  transition: color 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  min-height: 20px;
}

.requirement-text-clickable:hover {
  color: #409eff;
  background-color: #f0f9ff;
}

/* 需求背景详情弹窗样式 */
.requirement-detail-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #409eff;
}

.requirement-detail-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.requirement-detail-content {
  max-height: 500px;
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
}

.requirement-text-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.8;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
  background: transparent;
  border: none;
  padding: 10px;
  min-height: 100px;
}

/* 需求背景弹窗中的HTML样式 */
.requirement-text-content p {
  margin: 8px 0;
  line-height: 1.6;
}

.requirement-text-content ul, .requirement-text-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.requirement-text-content li {
  margin: 4px 0;
  line-height: 1.6;
}

.requirement-text-content a {
  color: #409eff;
  text-decoration: none;
}

.requirement-text-content a:hover {
  text-decoration: underline;
}

.requirement-text-content h1, .requirement-text-content h2, .requirement-text-content h3,
.requirement-text-content h4, .requirement-text-content h5, .requirement-text-content h6 {
  margin: 12px 0 8px 0;
  font-weight: bold;
  color: #303133;
}

.requirement-text-content strong, .requirement-text-content b {
  font-weight: bold;
  color: #303133;
}

.requirement-text-content em, .requirement-text-content i {
  font-style: italic;
}

/* 需求背景详情弹窗滚动条样式 */
.requirement-detail-content::-webkit-scrollbar {
  width: 8px;
}

.requirement-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.requirement-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.requirement-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
