<!--组辅导数据统计-->
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="40px">
      <el-form-item label="组" prop="groupId">
        <el-select v-model="queryParams.groupId" placeholder="请选择组">
          <el-option v-for="dict in groupDict" :key="dict.deptId" :label="dict.deptName" :value="dict.deptId"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年" prop="year">
        <el-select v-model="queryParams.year" clearable placeholder="请选择年">
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year"/>
        </el-select>
      </el-form-item>
      <el-form-item label="月" prop="month">
        <el-select v-model="queryParams.month" clearable placeholder="请选择月">
          <el-option v-for="month in monthOptions" :key="month" :label="month" :value="month"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-tag style="margin-left: 10px" type="info">实时统计</el-tag>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-label">总辅导次数</div>
              <div class="stat-number">{{ statData.toturTotalCount || 0 }}</div>
            </div>
            <div class="stat-icon total">
              <i class="el-icon-data-analysis"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-label">辅导组员</div>
              <div class="stat-number">{{ statData.toturGroupMemberCount || 0 }}</div>
            </div>
            <div class="stat-icon member">
              <i class="el-icon-user-solid"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-label">平均辅导次数</div>
              <div class="stat-number">{{ statData.toturGroupMemberAvgCount || 0 }}</div>
            </div>
            <div class="stat-icon avg">
              <i class="el-icon-pie-chart"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-label">辅导最多组员</div>
              <div class="stat-number">{{ statData.maxTutorNickname || '暂无辅导数据' }}</div>
            </div>
            <div class="stat-icon max">
              <i class="el-icon-star-on"></i>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-bottom: 20px">
      <!-- 组员被辅导次数趋势图 -->
      <el-col :span="24">
        <el-card>
          <div ref="trendChart" style="width: 100%; height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 辅导指标类型分布图 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="24">
        <el-card>
          <div ref="distributionChart" style="width: 100%; height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import { groupTutorStat } from "@/api/business/performanceGuidanceManagement";
import { deptAuthSelect } from "@/api/commonBiz";
import * as echarts from 'echarts';

export default {
  name: "GroupTutorStat",
  data() {
    return {
      queryParams: {
        groupId: '', // 所属组
        year: '', // 年
        month: '' // 月
      },
      groupDict: [], // 组集合
      yearOptions: [], // 年份选项
      monthOptions: [], // 月份选项
      // 统计数据
      statData: {
        toturTotalCount: 0,
        toturGroupMemberCount: 0,
        toturGroupMemberAvgCount: 0,
        maxTutorNickname: '',
        trendDataList: [],
        indicatorTypeDataList: []
      },
      trendChart: null,
      distributionChart: null
    };
  },
  async created() {
    await this.initYearMonthOptions();
    await this.getDeptList();
    // 默认选中第一个组
    if (this.groupDict && this.groupDict.length > 0) {
      this.queryParams.groupId = this.groupDict[0].deptId;
    }
    this.getStatData();
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose();
    }
    if (this.distributionChart) {
      this.distributionChart.dispose();
    }
  },
  methods: {
    /** 初始化年月选项 */
    initYearMonthOptions() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      for (let i = 0; i < 5; i++) {
        this.yearOptions.push(currentYear - i);
      }
      for (let i = 1; i <= 12; i++) {
        this.monthOptions.push(i);
      }
      this.queryParams.year = currentYear;
      this.queryParams.month = currentMonth;
    },
    /** 查询部门列表 */
    getDeptList() {
      return deptAuthSelect().then(response => {
        this.groupDict = response.data || [];
        // 如果还未选择组，默认第一个组
        if (!this.queryParams.groupId && this.groupDict.length > 0) {
          this.queryParams.groupId = this.groupDict[0].deptId;
        }
      });
    },
    /** 查询统计数据 */
    getStatData() {
      const params = { ...this.queryParams };
      groupTutorStat(params).then(response => {
        this.statData = response.data || {};
        this.updateCharts();
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getStatData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      // 保留组，重置年月
      const currentGroupId = this.queryParams.groupId || (this.groupDict[0] && this.groupDict[0].deptId);
      this.queryParams = {
        groupId: currentGroupId,
        year: currentYear,
        month: currentMonth
      };
      this.handleQuery();
    },
    /** 初始化图表 */
    initCharts() {
      this.trendChart = echarts.init(this.$refs.trendChart);
      this.distributionChart = echarts.init(this.$refs.distributionChart);
      window.addEventListener('resize', this.handleResize);
    },
    /** 更新图表 */
    updateCharts() {
      this.updateTrendChart();
      this.updateDistributionChart();
    },
    /** 更新趋势图 */
    updateTrendChart() {
      if (!this.trendChart) return;
      const trendData = this.statData.trendDataList || [];
      // 获取所有日期
      const dates = trendData.length > 0 ? trendData[0].dates || [] : [];
      const series = trendData.map((item, index) => ({
        name: item.memberName,
        type: 'line',
        data: item.tutorCounts || [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2
        }
      }));
      const option = {
        title: {
          text: '组员被辅导次数变化趋势',
          left: 'left',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          top: '5%',
          right: '5%',
          type: 'scroll'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates,
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
      this.trendChart.setOption(option, true);
    },
    /** 更新分布图 */
    updateDistributionChart() {
      if (!this.distributionChart) return;
      const indicatorData = this.statData.indicatorTypeDataList || [];
      const option = {
        title: {
          text: '辅导指标类型分布',
          left: 'left'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: indicatorData.map(item => item.indicatorTypeName),
          axisTick: { alignWithLabel: true },
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '数量',
            type: 'bar',
            data: indicatorData.map(item => item.count),
          }
        ]
      };

      this.distributionChart.setOption(option, true);
    },

    /** 处理窗口大小变化 */
    handleResize() {
      if (this.trendChart) {
        this.trendChart.resize();
      }
      if (this.distributionChart) {
        this.distributionChart.resize();
      }
    }
  }
};
</script>

<style scoped>
.stat-card {
  padding: 2px 4px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;

  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.stat-icon.member {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.stat-icon.avg {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.stat-icon.max {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

</style>
