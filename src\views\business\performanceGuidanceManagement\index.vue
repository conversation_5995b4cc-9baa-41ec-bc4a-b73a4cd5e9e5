<!--绩效辅导管理-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="一类指标" prop="firstIndecator">
        <el-select v-model="queryParams.firstIndecator" clearable @change="handleIndecator(queryParams.firstIndecator)">
          <el-option
            v-for="dict in firstIndicators"
            :key="dict.code"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="二类指标" prop="secondIndecator">
        <el-select v-model="queryParams.secondIndecator" clearable :disabled="queryParams.firstIndecator === ''">
          <el-option
            v-for="dict in secondIndicators"
            :key="dict.code"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="绩效级别" prop="level">
        <el-select v-model="queryParams.level" clearable>
          <el-option
            v-for="dict in dict.type.level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          clearable
        />
      </el-form-item>
      <el-form-item label="岗位" prop="personType">
        <el-select v-model="queryParams.personType" clearable>
          <el-option
            v-for="dict in dict.type.person_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属组" prop="groupId">
        <el-select v-model="queryParams.groupId" clearable>
          <el-option :value=null label="全部"></el-option>
          <el-option
            v-for="dict in groupDict"
            :key="dict.deptId"
            :label="dict.deptName"
            :value="dict.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="事件发生时间">
          <el-date-picker v-model="queryParams.eventTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间时间">
        <el-date-picker v-model="queryParams.createTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                        :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否辅导" prop="tutorFlag" >
        <el-select v-model="queryParams.tutorFlag" clearable placeholder="请选择">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否建议" prop="suggestFlag">
        <el-select v-model="queryParams.suggestFlag" clearable placeholder="请选择">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="stat-buttons" style="margin-bottom: 20px;">
      <el-button type="primary" icon="el-icon-data-line" size="mini" @click="goToDeptStat">部门辅导数据统计</el-button>
      <el-button type="primary" icon="el-icon-data-line" size="mini" @click="goToGroupStat">组辅导数据统计</el-button>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%;" stripe border height="calc(100vh - 280px)" @sort-change="handleSortChange">
      <el-table-column label="反馈编码" align="center" prop="feedbackCode" width="160" />
      <el-table-column label="一类指标" align="center" prop="primaryIndicatorName" width="130" />
      <el-table-column label="二类指标" align="center" prop="secondaryIndicatorName" width="160" />
      <el-table-column label="事件标题" align="center" prop="eventTitle" width="300" />
      <el-table-column label="事件发生时间" align="center" width="300">
        <template slot-scope="scope">
          {{scope.row.eventStartTime}} - {{scope.row.eventEndTime}}
        </template>
      </el-table-column>
      <el-table-column label="所属组" align="center" prop="groupName" width="140" />
      <el-table-column label="岗位" align="center" prop="roleName" width="120" />
      <el-table-column label="姓名" align="center" prop="nickName" width="120" />
      <el-table-column label="推荐绩效级别" align="center" prop="recommendedLevel" width="120" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="辅导概要" align="center" prop="tutoringSummary" :show-overflow-tooltip="true" width="300" />
      <el-table-column label="辅导结果" align="center" prop="tutoringResult" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="辅导人" align="center" prop="tutor" width="120" />
      <el-table-column label="辅导时间" align="center" prop="tutoringTime" width="140" sortable="custom" />
      <el-table-column label="总监建议" align="center" prop="directorSuggest"  :show-overflow-tooltip="true" width="200" />
      <el-table-column label="建议时间" align="center" prop="suggestTime" width="160" sortable="custom" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="showDetail(scope.row)">查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-guide"
            v-hasPermi="['system:performanceTutoring:tutoring']"
            @click="handleGuidance(scope.row)"
          >绩效辅导</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-postcard"
            v-hasPermi="['system:performanceTutoring:suggest']"
            @click="handleSuggestion(scope.row)"
          >总监建议</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            :disabled="scope.row.tutoringAttachment === null"
            @click="handleDownload(scope.row.tutoringAttachment)"
          >附件下载</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 绩效辅导记录登记 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-descriptions title="事件明细" :column="1"  style="margin-bottom: 20px">
        <el-descriptions-item label="姓名">{{ event.nickName }}</el-descriptions-item>
        <el-descriptions-item label="绩效等级">{{ event.recommendedLevel }}</el-descriptions-item>
        <el-descriptions-item label="一级指标">{{ event.primaryIndicatorName }}</el-descriptions-item>
        <el-descriptions-item label="二级指标">{{ event.secondaryIndicatorName }}</el-descriptions-item>
        <el-descriptions-item label="事件明细">{{ event.eventDetail }}</el-descriptions-item>
        <el-descriptions-item label="推荐理由">{{ event.recommendedReason }}</el-descriptions-item>
      </el-descriptions>
      <el-form ref="formData" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="辅导人" prop="tutor">
          <el-input v-model="formData.tutor" style="width: 220px"/>
        </el-form-item>
        <el-form-item label="辅导时间" prop="tutoringTime">
          <el-date-picker
            v-model="formData.tutoringTime"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="辅导结果" prop="tutoringResult">
          <el-input v-model="formData.tutoringResult" type="textarea" :rows="3" maxlength="100" show-word-limit placeholder="1、经过本次辅导，已知悉小王近期状态不佳原因，沟通后决定小王休假两天调整心情和状态。
2、小王休假回来后，状态明显好转，未再出现失误，积极性较之前有提高。" />
        </el-form-item>
        <el-form-item label="辅导概要" prop="tutoringSummary">
          <el-input v-model="formData.tutoringSummary" type="textarea" :rows="10" maxlength="1500" show-word-limit placeholder="" />
          <el-popover
            placement="right"
            title="示例模板"
            width="600"
            trigger="click"
            popper-class="custom-popover"
            :content="popoverContent">
            <el-button slot="reference" type="text">查看示例模板</el-button>
          </el-popover>
        </el-form-item>
        <el-form-item label="" prop="tutoringAttachment">
          <el-upload
            ref="uploadAudio"
            action=""
            :on-exceed="handleLimit"
            :show-file-list="true"
            :limit="5"
            :multiple="true"
            :http-request="handleUpload"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove">
            <el-button type="primary">上传附件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="tutoringPerformanceTutoring" :loading="btnLoading">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 总监建议 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" append-to-body>
      <el-descriptions title="事件明细" :column="1"  style="margin-bottom: 20px">
        <el-descriptions-item label="姓名">{{ event.nickName }}</el-descriptions-item>
        <el-descriptions-item label="绩效等级">{{ event.recommendedLevel }}</el-descriptions-item>
        <el-descriptions-item label="一级指标">{{ event.primaryIndicatorName }}</el-descriptions-item>
        <el-descriptions-item label="二级指标">{{ event.secondaryIndicatorName }}</el-descriptions-item>
        <el-descriptions-item label="事件明细">{{ event.eventDetail }}</el-descriptions-item>
        <el-descriptions-item label="推荐理由">{{ event.recommendedReason }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="组长辅导记录" :column="1"  style="margin-bottom: 20px">
        <el-descriptions-item label="辅导人">{{ totur.tutor }}</el-descriptions-item>
        <el-descriptions-item label="辅导时间">{{ totur.tutoringTime }}</el-descriptions-item>
        <el-descriptions-item label="辅导结果">{{ totur.tutoringResult }}</el-descriptions-item>
        <el-descriptions-item label="辅导概要">{{ totur.tutoringSummary }}</el-descriptions-item>
      </el-descriptions>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules2" label-position="top" label-width="80px">
        <span style="font-weight: bold;color: #090909;margin-bottom: 20px;font-size: medium">总监建议</span>
        <el-form-item label="请填写" prop="directorSuggest">
          <el-input v-model="ruleForm.directorSuggest" type="textarea" :rows="5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="suggestPerformanceTutoring" :loading="btnLoading">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 查看 -->
    <detailDialog :dialogVisible.sync="detailDialog.show" :dialogData="detailDialog.data"></detailDialog>
  </div>
</template>

<script>
import {
  getIndicatorList,
  getPerformanceTutoringList,
  suggestPerformanceTutoring,
  tutoringPerformanceTutoring,
  delFiles,
  uploadBatch,
  downloadBatch,
  getLastEditTotur
} from "@/api/business/performanceGuidanceManagement";
import {getToken} from "@/utils/auth";
import {deptSelect} from "@/api/commonBiz";
import {postBlob} from "@/utils/request";
import detailDialog from './components/detailDialog.vue'

export default {
  name: "Post",
  dicts: ['person_type', 'level'],
  components: { detailDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      tableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      dialogVisible: false, // 总监建议弹窗
      btnLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: '', //	排序列
        isAsc: '', // 排序的方向desc或者asc
        firstIndecator: '', // 一类指标
        secondIndecator: '', // 二类指标
        level: '', // 绩效级别(S/A/B/C/D)
        nickName: '', // 	姓名
        personType: '', // 	岗位
        groupId: '', // 所属组
        eventTime: [], // 事件发生时间范围
        createTime: [],
        tutorFlag: null, // 是否辅导
        suggestFlag: null // 是否建议
      },
      groupDict:[], // 组集合
      firstIndicators: [], // 一类指标
      secondIndicators: [], // 二类指标
      // 表单参数
      formData: {
        id: '',
        feedbackId: '', // 	绩效反馈表id(tb_performance_feedback)
        tutoringSummary: '', // 辅导概要
        tutoringResult: '', // 辅导结果
        tutoringTime: '', // 辅导时间
        tutor: '', // 辅导人
        tutoringAttachment: '' // 辅导附件
      },
      fileList: [], // 附件列表
      // 总监建议弹窗
      ruleForm: {
        id: '',
        directorSuggest: '',
        feedbackId: ''
      },
      //事件
      event: {
        nickName: '',// 姓名
        recommendedLevel: '', // 推荐级别
        primaryIndicatorName: '',// 一类指标
        secondaryIndicatorName: '',// 二类指标
        eventDetail: '',// 事件明细
        recommendedReason: ''//推荐原因
      },
      // 辅导记录
      totur: {
        tutoringSummary: '', // 辅导概要
        tutoringResult: '', // 辅导结果
        tutoringTime: '', // 辅导时间
        tutor: '', // 辅导人
      },
      newFormData: new FormData(),
      popoverContent: `格式：人员+背景+辅导目标+辅导过程+被辅导人反馈+后续观察情况\n示例：近期，小王在工作中失误频发，操作时频频走神，情绪也持续低落，致使生产环节出现疏漏。察觉到这一异常后，团队迅速启动绩效辅导机制，旨在深入了解其状态波动的根源，并制定针对性改善方案。\n辅导过程中，小王坦诚道出缘由：近期生活与工作压力交织，长期睡眠不足，导致精神难以集中，在工位上也时常陷入恍惚状态，工作效率与质量均受影响。经过充分沟通与疏导，双方达成共识，小王决定申请两天休假，暂别工作环境，选择外出旅行，通过亲近自然、调整作息来舒缓身心。\n休假归来后，经过一周的持续观察，小王精神面貌焕然一新，脸上重现往日的笑容，工作时专注度大幅提升，操作精准规范，未再出现任何失误，状态已基本恢复至正常水平，此次绩效辅导成功助力小王走出低迷，回归高效工作轨道。`,
      // 表单校验
      rules: {
        tutoringResult: [
          { required: true, message: "辅导结果不能为空", trigger: "blur" }
        ],
        tutoringSummary: [
          { required: true, message: "辅导概要不能为空", trigger: "blur" }
        ],
        tutor: [
          { required: true, message: "辅导人不能为空", trigger: "blur" }
        ],
        tutoringTime: [
          { required: true, message: "辅导时间不能为空", trigger: "change" }
        ]
      },
      rules2: {
        directorSuggest: [
          { required: true, message: "总监建议不能为空", trigger: "blur" }
        ]
      },
      // 查看弹窗
      detailDialog: {
        show: false,
        data: {}
      }
    };
  },
  created() {
    this.getDeptList();
    this.getIndicatorList()
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = { ...this.queryParams };
      if (params.eventTime && params.eventTime.length === 2) {
        params.eventStartTime = params.eventTime[0];
        params.eventEndTime = params.eventTime[1];
        delete params.eventTime;
      }
      if (params.createTime && params.createTime.length === 2) {
        params.createStartTime = params.createTime[0];
        params.createEndTime = params.createTime[1];
        delete params.createTime;
      }
      getPerformanceTutoringList(params).then(response => {
        this.tableData = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 一类指标 */
    getIndicatorList() {
      getIndicatorList({}).then(response => {
        this.firstIndicators = response.data
      });
    },
    /** 二类指标 */
    handleIndecator(code) {
      this.queryParams.secondIndecator = ''
      this.firstIndicators.forEach((item) => {
        if (item.code === code) {
          this.secondIndicators = item.secondaryIndicators
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.dialogVisible = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.formData = {
        id: '',
        feedbackId: '', // 	绩效反馈表id(tb_performance_feedback)
        tutoringSummary: '', // 辅导概要
        tutoringResult: '', // 辅导结果
        tutoringTime: '', // 辅导时间
        tutor: '', // 辅导人
        tutoringAttachment: '' // 辅导附件
      }
      this.fileList = []
      const keys = [...this.newFormData.keys()];
      // 遍历删除
      keys.forEach(key => {
        this.newFormData.delete(key); // 删除该字段所有值
      });
      this.resetForm("formData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.eventTime = []
      this.queryParams.createTime = []
      this.handleQuery();
    },
    /** 总监建议弹窗 */
    handleSuggestion(row) {
      this.dialogVisible = true;
      this.title = "总监建议";
      this.ruleForm.id = row.id
      this.ruleForm.directorSuggest = row.directorSuggest
      this.ruleForm.feedbackId = row.feedbackId
      //辅导记录
      this.totur.tutoringSummary = row.tutoringSummary
      this.totur.tutoringResult = row.tutoringResult
      this.totur.tutoringTime = row.tutoringTime
      this.totur.tutor = row.tutor
      //事件详情
      this.event.nickName = row.nickName
      this.event.recommendedLevel = row.recommendedLevel
      this.event.primaryIndicatorName = row.primaryIndicatorName
      this.event.secondaryIndicatorName = row.secondaryIndicatorName
      this.event.eventDetail = row.eventDetail
      this.event.recommendedReason = row.recommendedReason
    },
    suggestPerformanceTutoring () {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          this.btnLoading = true
          suggestPerformanceTutoring(this.ruleForm).then(res => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.dialogVisible = false
              this.getList()
            } else {
              this.$message.error(res.msg)
            }
          });
        }
      });
    },
    /** 辅导绩效 */
    handleGuidance(row) {
      const keys = [...this.newFormData.keys()];
      // 遍历删除
      keys.forEach(key => {
        this.newFormData.delete(key); // 删除该字段所有值
      });
      this.open = true;
      this.title = "绩效辅导记录登记";
      this.formData.id = row.id
      this.formData.tutoringSummary = row.tutoringSummary
      this.formData.feedbackId = row.feedbackId
      this.formData.tutoringResult = row.tutoringResult
      this.formData.tutoringTime = row.tutoringTime
      this.formData.tutor = row.tutor
      if (!row.tutor) { // 若辅导人为空，则获取上次的辅导人
        getLastEditTotur().then(res => {
          this.formData.tutor = res.data
        })
      }
      this.formData.tutoringAttachment = row.tutoringAttachment
      if (row.tutoringAttachment !== '' && row.tutoringAttachment !== null) {
        this.fileList = [{
          fileId: '',
          name: this.getAfterLastUnderscore(row.tutoringAttachment),
          url: row.tutoringAttachment
        }]
      } else {
        this.fileList = []
      }
      this.event.nickName = row.nickName
      this.event.recommendedLevel = row.recommendedLevel
      this.event.primaryIndicatorName = row.primaryIndicatorName
      this.event.secondaryIndicatorName = row.secondaryIndicatorName
      this.event.eventDetail = row.eventDetail
      this.event.recommendedReason = row.recommendedReason
      if (this.$refs.formData) {
        this.$nextTick(() => {
          this.$refs.formData.clearValidate(['tutoringTime']);
        })
      }
    },
    tutoringPerformanceTutoring: function() {
      this.$refs["formData"].validate(valid => {
        if (valid) {
          this.btnLoading = true
          tutoringPerformanceTutoring(this.formData).then(res => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.open = false
              this.getList()
            } else {
              this.$message.error(res.msg)
            }
          });
        }
      });
    },

    // 附件 限制
    handleLimit (file, type) {
      this.$message.info('只能上传5个附件文件！')
    },
    // 上传附件
    handleUpload (item) {
      this.newFormData.append('files', item.file)
      uploadBatch(this.newFormData).then(response => {
        this.formData.tutoringAttachment = response.data
        this.$modal.msgSuccess(response.msg)
      });
    },
    // 删除附件
    handleRemove (file) {
      console.log(1, file)
      console.log(2, this.formData.tutoringAttachment)
      let imgArr = this.formData.tutoringAttachment.split(',')
      for (let i = 0; i < imgArr.length; i++) {
        if (file.name === this.getAfterLastUnderscore(imgArr[i])) {
          delFiles(imgArr[i]).then(response => {
            this.$modal.msgSuccess(response.msg)
            this.formData.tutoringAttachment.splice(i, 1)
            this.newFormData.delete('file')
          });
        }
      }

    },
    // 上传录音/图片/附件证明判断
    beforeUpload (file) {
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!isLt200M) {
        this.$modal.msgError('上传文件大小不能超过200MB!')
      }
      return isLt200M
    },
    // 下载
    handleDownload (filePaths) {
      let formData = new FormData()
      formData.append('filePaths', filePaths)
      postBlob(`/imp-admin/system/files/downloadBatch`, formData)

    },
    // 获取文件名
    getAfterLastUnderscore (path) {
      if (path !== null && path !== '') {
        const filename = path.substring(path.lastIndexOf('/') + 1); // 提取文件名
        const lastUnderscoreIndex = filename.lastIndexOf('_');
        return filename.substring(lastUnderscoreIndex + 1); // 从下划线后取内容
      }
    },
    /** 查询部门列表 */
    getDeptList() {
      deptSelect().then(response => {
        this.groupDict = response.data
      })
    },
    /** 查看 */
    showDetail(row) {
      this.detailDialog.data = row
      this.detailDialog.show = true
    },
    /** 排序处理 */
    handleSortChange({ column, prop, order }) {
      if (order) {
        this.queryParams.orderByColumn = prop;
        this.queryParams.isAsc = order === 'ascending' ? 'asc' : 'desc';
      } else {
        this.queryParams.orderByColumn = '';
        this.queryParams.isAsc = '';
      }
      this.getList();
    },
    /** 跳转到部门辅导数据统计页面 */
    goToDeptStat() {
      this.$router.push({ path: '/performanceresult/techCenterTutorStat' });
    },

    /** 跳转到组辅导数据统计页面 */
    goToGroupStat() {
      this.$router.push({ path: '/performanceresult/groupTutorStat' });
    }
  }
};
</script>
<style scoped>
.dialog-footer {
  text-align: center;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 800px !important;
}
.custom-popover {
  white-space: pre-line; /* 关键样式：识别换行符 */
}
</style>
