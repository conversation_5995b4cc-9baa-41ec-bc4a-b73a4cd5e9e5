<!--部门合作度数据统计-->
<template>
  <div class="app-container">
    <!-- 时间选择器 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
      <el-form-item label="年份" prop="yearValue">
        <el-select v-model="queryParams.yearValue" placeholder="选择年份" @change="getStatistics">
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="月份" prop="monthValue">
        <el-select v-model="queryParams.monthValue" placeholder="选择月份" @change="getStatistics" clearable>
          <el-option
            v-for="dict in dict.type.month"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <span style="color: #999; font-size: 12px;">
          数据更新时间：{{ updateTimeFormatted }}
        </span>
      </el-form-item>
    </el-form>

    <!-- 基础统计数据卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="8">
        <div class="statistics-card submit-count">
          <div class="card-icon">
            <i class="el-icon-data-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">提交数量</div>
            <div class="card-value">{{ statistics.basicStatistics.submitCount || 0 }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="statistics-card praise-count">
          <div class="card-icon">
            <i class="el-icon-thumb"></i>
          </div>
          <div class="card-content">
            <div class="card-title">表扬数量</div>
            <div class="card-value">{{ statistics.basicStatistics.praiseCount || 0 }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="statistics-card improvement-count">
          <div class="card-icon">
            <i class="el-icon-warning"></i>
          </div>
          <div class="card-content">
            <div class="card-title">待改进数量</div>
            <div class="card-value">{{ statistics.basicStatistics.improvementCount || 0 }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="12">
        <el-card class="ranking-card">
          <div slot="header" class="clearfix">
            <span>个人被表扬次数 TOP10</span>
            <i class="el-icon-question" style="float: right; color: #999; cursor: help;" title="按表扬次数排序">按表扬次数排序</i>
          </div>
          <div class="ranking-list">
            <div class="ranking-header">
              <span class="ranking-rank">排名</span>
              <span class="ranking-name">姓名</span>
              <span class="ranking-count">表扬次数</span>
            </div>
            <div 
              v-for="(item, index) in statistics.praiseTop10" 
              :key="index"
              class="ranking-item"
            >
              <span class="ranking-rank">
                <span 
                  :class="['rank-number', index < 3 ? 'top-three' : '']"
                  :style="getRankStyle(index)"
                >
                  {{ index + 1 }}
                </span>
              </span>
              <span class="ranking-name">{{ item.employeeName }}</span>
              <span class="ranking-count">{{ item.count }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="ranking-card">
          <div slot="header" class="clearfix">
            <span>个人待改进次数 TOP10</span>
            <i class="el-icon-question" style="float: right; color: #999; cursor: help;" title="按待改进次数排序">按待改进次数排序</i>
          </div>
          <div class="ranking-list">
            <div class="ranking-header">
              <span class="ranking-rank">排名</span>
              <span class="ranking-name">姓名</span>
              <span class="ranking-count">待改进次数</span>
            </div>
            <div 
              v-for="(item, index) in statistics.improvementTop10" 
              :key="index"
              class="ranking-item"
            >
              <span class="ranking-rank">
                <span 
                  :class="['rank-number', index < 3 ? 'top-three' : '']"
                  :style="getRankStyle(index)"
                >
                  {{ index + 1 }}
                </span>
              </span>
              <span class="ranking-name">{{ item.employeeName }}</span>
              <span class="ranking-count">{{ item.count }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 按组统计 -->
    <el-card style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span>按组统计</span>
        <div style="float: right;">
          <el-tag type="primary" size="mini" style="margin-right: 10px;">表扬</el-tag>
          <el-tag type="success" size="mini">待改进</el-tag>
        </div>
      </div>
      <div id="groupChart" style="width: 100%; height: 400px;"></div>
    </el-card>

    <!-- 按岗位统计 -->
    <el-card>
      <div slot="header" class="clearfix">
        <span>按岗位统计</span>
        <div style="float: right;">
          <el-tag type="primary" size="mini" style="margin-right: 10px;">表扬</el-tag>
          <el-tag type="success" size="mini">待改进</el-tag>
        </div>
      </div>
      <div id="positionChart" style="width: 100%; height: 400px;"></div>
    </el-card>

    <!-- 趋势图表 -->
    <el-card style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span>表扬与待改进数量趋势</span>
        <div style="float: right;">
          <el-tag type="primary" size="mini" style="margin-right: 10px;">表扬数量</el-tag>
          <el-tag type="success" size="mini">待改进数量</el-tag>
        </div>
      </div>
      <div id="trendChart" style="width: 100%; height: 400px;"></div>
    </el-card>
  </div>
</template>

<script>
import { getDeptCooperationInterviewStatistics, getDeptCooperationInterviewTimeTrends } from "@/api/process/monthlyDepartmentCooperationInterview";
import * as echarts from 'echarts';

export default {
  name: "DeptCooperationStatistics",
  dicts: ['month'],

  data() {
    return {
      // 查询参数
      queryParams: {
        yearValue: new Date().getFullYear(),
        monthValue: null
      },
      // 年份选项
      yearOptions: [],
      // 统计数据
      statistics: {
        basicStatistics: {
          submitCount: 0,
          praiseCount: 0,
          improvementCount: 0
        },
        praiseTop10: [],
        improvementTop10: [],
        timeTrends: [],
        groupStatistics: [],
        positionStatistics: [],
        updateTime: null
      },
      // 图表实例
      trendChart: null,
      groupChart: null,
      positionChart: null
    };
  },
  computed: {
    updateTimeFormatted() {
      if (this.statistics.updateTime) {
        return this.parseTime(this.statistics.updateTime, '{y}-{m}-{d} {h}:{i}:{s}');
      }
      return '';
    }
  },
  created() {
    this.initYearOptions();
  },
  mounted() {
    this.getStatistics();
    this.$nextTick(() => {
      this.initCharts();
    });
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.trendChart) {
      this.trendChart.dispose();
    }
    if (this.groupChart) {
      this.groupChart.dispose();
    }
    if (this.positionChart) {
      this.positionChart.dispose();
    }
  },
  methods: {
    /** 初始化年份选项 */
    initYearOptions() {
      const currentYear = new Date().getFullYear();
      for (let i = 0; i < 5; i++) {
        this.yearOptions.push(currentYear - i);
      }
    },
    /** 获取统计数据 */
    getStatistics() {
      const params = { ...this.queryParams };
      // 过滤空值
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === '') {
          delete params[key];
        }
      });

      // 并行调用两个接口
      Promise.all([
        getDeptCooperationInterviewStatistics(params),
        getDeptCooperationInterviewTimeTrends(this.queryParams.yearValue)
      ]).then(([statisticsResponse, trendsResponse]) => {
        console.log('统计数据接口返回:', statisticsResponse);
        console.log('趋势数据接口返回:', trendsResponse);

        // 合并数据
        const statisticsData = statisticsResponse.data || {
          basicStatistics: {
            submitCount: 0,
            praiseCount: 0,
            improvementCount: 0
          },
          praiseTop10: [],
          improvementTop10: [],
          groupStatistics: [],
          positionStatistics: [],
          updateTime: null
        };

        // 将趋势数据单独设置
        this.statistics = {
          ...statisticsData,
          timeTrends: trendsResponse.data || []
        };

        console.log('设置后的statistics:', this.statistics);
        this.$nextTick(() => {
          console.log('开始更新图表...');
          this.updateCharts();
        });
      }).catch(error => {
        console.error('获取统计数据失败:', error);
        this.$message.error('获取统计数据失败，请稍后重试');
        // 设置默认数据防止页面崩溃
        this.statistics = {
          basicStatistics: {
            submitCount: 0,
            praiseCount: 0,
            improvementCount: 0
          },
          praiseTop10: [],
          improvementTop10: [],
          timeTrends: [],
          groupStatistics: [],
          positionStatistics: [],
          updateTime: null
        };
        this.$nextTick(() => {
          this.updateCharts();
        });
      });
    },
    /** 获取排名样式 */
    getRankStyle(index) {
      const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // 金、银、铜
      if (index < 3) {
        return {
          backgroundColor: colors[index],
          color: '#fff',
          fontWeight: 'bold'
        };
      }
      return {};
    },
    /** 初始化图表 */
    initCharts() {
      const trendEl = document.getElementById('trendChart');
      const groupEl = document.getElementById('groupChart');
      const positionEl = document.getElementById('positionChart');
      
      if (trendEl) {
        this.trendChart = echarts.init(trendEl);
      }
      if (groupEl) {
        this.groupChart = echarts.init(groupEl);
      }
      if (positionEl) {
        this.positionChart = echarts.init(positionEl);
      }
      
      // 窗口大小变化时重新调整图表
      window.addEventListener('resize', () => {
        this.trendChart && this.trendChart.resize();
        this.groupChart && this.groupChart.resize();
        this.positionChart && this.positionChart.resize();
      });
    },
    /** 更新图表 */
    updateCharts() {
      this.updateTrendChart();
      this.updateGroupChart();
      this.updatePositionChart();
    },
    /** 更新趋势图表 */
    updateTrendChart() {
      if (!this.trendChart) {
        console.log('trendChart 未初始化');
        return;
      }
      
      if (!this.statistics.timeTrends || this.statistics.timeTrends.length === 0) {
        console.log('趋势数据为空:', this.statistics.timeTrends);
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 16
            }
          }
        };
        this.trendChart.setOption(option);
        return;
      }
      
      const xAxisData = this.statistics.timeTrends.map(item => item.timeLabel);
      const praiseData = this.statistics.timeTrends.map(item => item.praiseCount);
      const improvementData = this.statistics.timeTrends.map(item => item.improvementCount);
      
      console.log('趋势图数据:', {xAxisData, praiseData, improvementData});
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['表扬数量', '待改进数量']
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '15%',
          bottom: '10%'
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '表扬数量',
            type: 'line',
            data: praiseData,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.3)'
            }
          },
          {
            name: '待改进数量',
            type: 'line',
            data: improvementData,
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            },
            areaStyle: {
              color: 'rgba(103, 194, 58, 0.3)'
            }
          }
        ]
      };
      
      this.trendChart.setOption(option, true);
    },
    /** 更新按组统计图表 */
    updateGroupChart() {
      if (!this.groupChart) {
        console.log('groupChart 未初始化');
        return;
      }
      
      if (!this.statistics.groupStatistics || this.statistics.groupStatistics.length === 0) {
        console.log('按组统计数据为空:', this.statistics.groupStatistics);
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 16
            }
          }
        };
        this.groupChart.setOption(option);
        return;
      }
      
      const categories = this.statistics.groupStatistics.map(item => item.groupName);
      const praiseData = this.statistics.groupStatistics.map(item => item.praiseCount);
      const improvementData = this.statistics.groupStatistics.map(item => item.improvementCount);
      
      console.log('按组统计数据:', {categories, praiseData, improvementData});
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['表扬', '待改进']
        },
        grid: {
          left: '15%',
          right: '10%',
          top: '10%',
          bottom: '10%'
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            interval: 0,
            fontSize: 12
          }
        },
        series: [
          {
            name: '表扬',
            type: 'bar',
            data: praiseData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '待改进',
            type: 'bar',
            data: improvementData,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      };
      
      this.groupChart.setOption(option, true);
    },
    /** 更新按岗位统计图表 */
    updatePositionChart() {
      if (!this.positionChart) {
        console.log('positionChart 未初始化');
        return;
      }
      
      if (!this.statistics.positionStatistics || this.statistics.positionStatistics.length === 0) {
        console.log('按岗位统计数据为空:', this.statistics.positionStatistics);
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 16
            }
          }
        };
        this.positionChart.setOption(option);
        return;
      }
      
      const categories = this.statistics.positionStatistics.map(item => item.positionName);
      const praiseData = this.statistics.positionStatistics.map(item => item.praiseCount);
      const improvementData = this.statistics.positionStatistics.map(item => item.improvementCount);
      
      console.log('按岗位统计数据:', {categories, praiseData, improvementData});
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['表扬', '待改进']
        },
        grid: {
          left: '15%',
          right: '10%',
          top: '10%',
          bottom: '10%'
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            interval: 0,
            fontSize: 12
          }
        },
        series: [
          {
            name: '表扬',
            type: 'bar',
            data: praiseData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '待改进',
            type: 'bar',
            data: improvementData,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      };
      
      this.positionChart.setOption(option, true);
    }
  }
};
</script>

<style scoped>
.statistics-card {
  display: flex;
  align-items: center;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.12);
  color: white;
  min-height: 130px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
}

.statistics-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.submit-count {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.praise-count {
  background: linear-gradient(135deg, #409EFF 0%, #3A8EE6 100%);
}

.improvement-count {
  background: linear-gradient(135deg, #67C23A 0%, #5DAF34 100%);
}

.card-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.8;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
}

.ranking-card {
  height: 500px;
}

.ranking-list {
  height: 430px;
  overflow-y: auto;
}

.ranking-header {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 2px solid #ebeef5;
  font-weight: bold;
  background-color: #f5f7fa;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s ease;
}

.ranking-item:hover {
  background-color: #f5f7fa;
}

.ranking-rank {
  width: 80px;
  text-align: center;
  padding-right: 15px;
}

.ranking-name {
  flex: 1;
  text-align: center;
  padding: 0 15px;
}

.ranking-count {
  width: 120px;
  text-align: center;
  font-weight: bold;
  color: #409eff;
  padding-left: 15px;
}

.rank-number {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  font-size: 12px;
}

.top-three {
  color: white !important;
}

.el-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
