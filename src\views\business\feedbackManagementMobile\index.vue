<!-- 新增编辑反馈弹窗 -->
<template>
  <div class="addEditFeedbackForm">
    <el-form ref="form" :model="formData" :rules="rules" inline>
      <el-form-item class="basic-info" label="基本信息">
        <el-card>
          <el-form-item label="事件标题" prop="eventTitle">
            <el-input v-model="formData.eventTitle" placeholder="事件标题" clearable style="width: 6.2rem" />
          </el-form-item>
          <el-form-item label="事件发生时间" prop="eventTimeRange">
            <el-date-picker v-model="formData.eventTimeRange" value-format="yyyy-MM-dd" type="daterange"  style="width: 6.2rem"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @click.native="calendarShow = true" readonly>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="审核项目经理" prop="projectManagerAuditor">
            <el-select v-model="formData.projectManagerAuditor" clearable style="width: 6.2rem">
              <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
            </el-select>
          </el-form-item>
          <el-form-item prop="eventDetail" class="introduction-item">
            <template slot="label">
              <span>事件明细</span>
              <el-button type="text" style="margin-left: .4rem;" @click="showTemplatePopup(eventTemplateList, '事件明细模版')">查看示例模板</el-button>
            </template>
            <el-input v-model="formData.eventDetail" placeholder="请详细描述事件经过，包括时间、地点、参与人员、具体行为等..." autocomplete="off" type="textarea" :rows="10" :maxlength="1000" show-word-limit></el-input>
          </el-form-item>
        </el-card>
      </el-form-item>
      <el-form-item label="推荐明细" prop="feedbackList" class="recommend-detail">
        <el-card v-for="(item, index) in formData.feedbackList" :key="index" :ref="`recommendDetail${index + 1}`">
          <el-button type="danger" class="remove-person" icon="el-icon-delete" size="mini" @click="removeRecommendPerson(index)">删除</el-button>
          <p class="title">推荐明细 #{{ index + 1 }}</p>
          <el-form-item label="人员" :prop="`feedbackList[${index}].nickName`" :rules="{ required: true, message: '请选择人员', trigger: 'change' }">
            <van-field
              v-model="formData.feedbackList[index].nickName"
              readonly style="width: 6.2rem" class="van-field-nickName"
              placeholder="请选择人员"
              @click="selectNickName(formData.feedbackList[index], 'nickName', `feedbackList[${index}].nickName`)"
              is-link
            />
          </el-form-item>
          <el-form-item label="一类指标" :prop="`feedbackList[${index}].primaryIndicator`" :rules="{ required: true, message: '请选择一类指标', trigger: 'change' }">
            <el-select v-model="formData.feedbackList[index].primaryIndicator"
                       clearable filterable style="width: 240px"
                       @change="changePrimaryIndicator(formData.feedbackList[index])">
              <el-option v-for="item in (primaryIndicatorOptions[index] || [])"
                         :key="item.code" :value="item.code" :label="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="二类指标" :prop="`feedbackList[${index}].secondaryIndicator`" :rules="{ required: true, message: '请选择二类指标', trigger: 'change' }">
            <el-select v-model="formData.feedbackList[index].secondaryIndicator"
                       clearable filterable style="width: 240px"
                       :disabled="!formData.feedbackList[index].primaryIndicator"
                       @change="changeSecondaryIndicator(formData.feedbackList[index])">
              <template v-for="item2 in (secondaryIndicatorOptions[index] || [])">
                <el-option v-if="item2.primaryIndicator === formData.feedbackList[index].primaryIndicator && !item2.systemGenerated"
                           :key="item2.code" :value="item2.code" :label="item2.name"/>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item label="推荐绩效" :prop="`feedbackList[${index}].recommendedLevel`" :rules="{ required: true, message: '请选择推荐绩效', trigger: 'change' }">
            <el-select v-model="formData.feedbackList[index].recommendedLevel" :disabled="!formData.feedbackList[index].secondaryIndicator" clearable style="width: 6.2rem" @change="formData.feedbackList[index].indicatorResultIds = []">
              <el-option v-for="item in dict.type.performance_level" :key="item.value" :value="item.value" :label="item.label" v-if="item.value !== 'B'" />
            </el-select>
          </el-form-item>
          <el-form-item label="推荐原因" v-if="formData.feedbackList[index].recommendedLevel">
              <template v-for="item in indicatorResultIdsOptions">
                <p :key="item.id" :value="item.id" class="indicatorResultP"
                  v-if="item.firstIndecator === formData.feedbackList[index].primaryIndicator && // 一类指标
                        item.secondIndecator === formData.feedbackList[index].secondaryIndicator && // 二类指标
                        item.level === formData.feedbackList[index].recommendedLevel">
                  {{ item.result }}
                </p>
              </template>
            </el-form-item>
          <el-form-item class="subjective-reason" :prop="`feedbackList[${index}].recommendedReason`" :rules="{ required: true, message: '请输入推荐理由', trigger: 'blur' }">
            <template slot="label">
              <span>推荐理由（请描述具体工作表现）</span>
              <el-button type="text" style="margin-left: .4rem;" @click="showTemplatePopup(recommendedReasonList, '推荐理由模版')">查看示例模板</el-button>
            </template>
            <el-input v-model="formData.feedbackList[index].recommendedReason" placeholder="请详细说明推荐该绩效级别的理由..." autocomplete="off" type="textarea" :rows="5" :maxlength="500" show-word-limit></el-input>
          </el-form-item>
        </el-card>
      </el-form-item>
    </el-form>
    <div label-width="0" class="btn-group">
      <el-button type="primary" class="add-recommend" icon="el-icon-plus" @click="addRecommendPerson">添加推荐</el-button>
      <el-button type="primary" @click="confirm" :loading="btnLoading">保存</el-button>
      <el-button @click="cancel">重置</el-button>
    </div>
    <!-- 选择人员弹窗 -->
    <van-popup v-model="userPopup.show" position="bottom" round style="height: 80vh;" class="user-popup">
      <van-search v-model="userPopup.searchKey" placeholder="请输入搜索关键词" @input="searchUser" />
      <van-radio-group v-model="userPopup.radioValue">
        <van-cell-group>
          <van-cell
            v-for="option in userPopup.allData"
            :key="option.userId"
            :title="option.nickName"
            clickable
            @click="handleSelect(option.nickName)"
          >
            <template #right-icon>
              <van-radio :name="option.nickName" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
      <van-button @click="confirmSelection" round type="info" class="confirm-button">确认</van-button>
    </van-popup>
    <!-- 选择日期范围弹窗 -->
    <van-calendar v-model:show="calendarShow" type="range" title="事件发生时间" @confirm="onConfirmCalendar" :default-date="calendarDate" :allow-same-day="true" :min-date="new Date(new Date().setMonth(new Date().getMonth() - 6))" color="#1890ff"/>
    <!-- 展示事件明细模版 -->
    <van-popup v-model="templatePopup.show" position="bottom" round style="height: 80vh;" class="template-list-popup">
      <div class="title">{{templatePopup.title}}</div>
      <div class="content">
        <div v-for="item in templatePopup.data" :key="item.name" style="white-space: break-spaces;">
          <span style="font-weight: bold;">{{ item.name }}：</span><br/>{{ item.content }}
        </div>
      </div>
      <van-button @click="templatePopup.show = false" round type="info" class="confirm-button">确认</van-button>
    </van-popup>
  </div>
</template>
<script>
import { performanceEventAdd, performIndicatorResultList, getRoleIndicator } from "@/api/business/performanceFeedback"
import { listUserAll } from "@/api/system/user"
import './font-size.js'
import { Field, Popup, RadioGroup, Radio, CellGroup, Cell, Search, Calendar, Button } from 'vant'
export default {
  dicts: [
    'project_outcome_project_manager',
    'performance_level'
  ],
  components: {
    'van-field': Field,
    'van-popup': Popup,
    'van-radio-group': RadioGroup,
    'van-radio': Radio,
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-search': Search,
    'van-calendar': Calendar,
    'van-button': Button
  },
  data() {
    return {
      btnLoading: false,
      selectedText: '',
      userPopup: { // 选择人员弹窗
        show: false,
        searchKey: '', // 筛选关键字
        bindData: null, // 需要绑定的object
        key: '', // 需要绑定object的key
        validateFieldKey: '', // 校验字段
        radioValue: null, // 单选框选中值
        allData: {},
        currentIndex: null
      },
      calendarShow: false, // 日期选择弹窗
      templatePopup: { // 模版弹窗
        show: false,
        title: '',
        data: []
      },
      formData: {
        id: '',
        primaryIndicator: '',
        secondaryIndicator: '',
        eventTitle: '',
        eventTimeRange: [],
        projectManagerAuditor: '',
        eventDetail: '',
        feedbackList: []
      },
      rules: {
        primaryIndicator: [ { required: true, message: '请选择一类指标', trigger: 'change' } ],
        secondaryIndicator: [ { required: true, message: '请选择二类指标', trigger: 'change' } ],
        eventTitle: [ { required: true, message: '请输入事件标题', trigger: 'blur' } ],
        eventTimeRange: [ { required: true, message: '请选择事件发生时间', trigger: 'change' } ],
        projectManagerAuditor: [ { required: true, message: '请选择审核项目经理', trigger: 'change' } ],
        eventDetail: [ { required: true, message: '请输入事件明细', trigger: 'change' } ],
        feedbackList: [ { required: true, message: '请添加推荐人员', trigger: 'change' } ]
      },
      // 一类指标下拉数据
      primaryIndicatorOptions: {},
      // 二类指标下拉数据
      secondaryIndicatorOptions: {},
      // 添加角色指标缓存
      roleIndicatorCache: {},
      // 全部用户下拉数据
      userOptions: [],
      // 推荐原因下拉数据
      indicatorResultIdsOptions: [],
      eventTemplateList: [
        { name: '事件明细（S级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 6 月 20 日，在12楼技术部会议室，项目组就系统升级项目召开评审会。会上，团队成员对技术实现方式产生激烈冲突，一方坚持采用旧框架降低风险，另一方力推新框架追求性能突破，僵持不下。小王全程准时参会，面对争论始终保持理性克制，未与任何人发生冲突。他深入分析项目需求与团队技术能力，创新性地提出混合使用新旧框架的方案，既提升性能又规避风险；针对系统兼容性难题，小王查阅大量资料，反复测试。最终，他成功找到适配方案，保障项目顺利推进，获得各部门认可。'},
        { name: '事件明细（A级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 7 月，小王承担重点项目-智能客服系统的开发与上线工作。面对多渠道接入、智能应答逻辑复杂等难题，他制定精细化开发计划，通过优化代码结构与并行开发，提前 4 天完成智能路由分配、语义理解核心模块。系统上线后，全程无回滚，稳定运行一周无生产故障。'},
        { name: '事件明细（C级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 7 月，小王参与客服系统优化项目。项目初期，领导安排他负责客服机器人知识库搭建，他以任务繁琐为由拒绝，导致该模块进度滞后。中期团队需要他协助测试新上线的智能转接功能，小王再次推脱，称自己不熟悉测试流程，使得功能存在的漏洞未及时发现，幸测试团队发现问题，未造成生产问题。'},
        { name: '事件明细（D级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 7 月，小王参与新客服系统迭代项目。工作时间内，他多次长时间使用办公电脑玩网络游戏，被同事和领导提醒后仍不改正。在一次项目讨论会上，因意见不合，小王与同事发生激烈争吵，甚至推搡对方，造成办公秩序混乱。这些行为不仅导致他负责的模块开发进度严重滞后，影响项目整体交付，还在团队内引发不良风气，损害公司内部和谐氛围。'},
      ],
      recommendedReasonList: [
        { name: '推荐理由（S级模板）', content: '小王严守会议纪律，沟通理性，提出创新方案并攻克技术难点，完全符合 S 绩效标准，值得推荐。' },
        { name: '推荐理由（A级模板）', content: '小王按时超量完成开发任务，系统稳定可靠，完全符合 A 绩效标准，值得推荐。' },
        { name: '推荐理由（C级模板）', content: '小王在项目中两次不服从工作任务安排，导致项目出现严重问题，符合 C 绩效评定标准，应评定为 C 绩效。' },
        { name: '推荐理由（D级模板）', content: '小王在上班期间做无关事项，且与同事发生肢体冲突，严重违反工作制度，后果严重，符合 D 绩效评定标准，应评定为 D 绩效。' },
      ]
    }
  },
  watch: {
  },
  computed: {
    calendarDate () {
      if (this.formData.eventTimeRange.length === 0) {
        return [new Date(), new Date()]
      } else {
        return [new Date(this.formData.eventTimeRange[0]), new Date(this.formData.eventTimeRange[1])]
      }
    }
  },
  created() {
    this.listUserAll()
    this.getIndicatorResultIdsOptions()
  },
  mounted() {
  },
  methods: {
    // 人员选择弹窗-筛选
    searchUser () {
      let searchData = this.userOptions.filter(item => item.nickName.indexOf(this.userPopup.searchKey) >= 0)
      this.userPopup.allData = searchData
    },
    // 人员选择弹窗-显示
    selectNickName (data, key, validateFieldKey) {
      this.userPopup.searchKey = ''
      this.userPopup.bindData = data
      this.userPopup.key = key
      this.userPopup.validateFieldKey = validateFieldKey
      this.userPopup.radioValue = data[key]
      this.userPopup.allData = [...this.userOptions]
      this.userPopup.show = true
      this.userPopup.currentIndex = this.formData.feedbackList.indexOf(data)
    },
    // 人员选择弹窗-选择
    handleSelect (value) {
      this.userPopup.radioValue = value
    },
    // 人员选择弹窗-确认选择
    async confirmSelection() {
      this.$set(this.userPopup.bindData, this.userPopup.key, this.userPopup.radioValue);
      this.userPopup.show = false;
      this.$refs.form.validateField(this.userPopup.validateFieldKey);
      if (this.userPopup.currentIndex !== undefined) {
        await this.changeUser(this.formData.feedbackList[this.userPopup.currentIndex], this.userPopup.currentIndex);
      }
    },
    // 时间选择弹窗-选择
    onConfirmCalendar (values) {
      const [start, end] = values
      const formatDate = (date) => `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
      this.formData.eventTimeRange = [formatDate(start), formatDate(end)]
      this.calendarShow = false
    },
    // 模版弹窗
    showTemplatePopup (data, title) {
      this.templatePopup.data = data
      this.templatePopup.title = title
      this.templatePopup.show = true
    },
    /** 取消 */
    cancel () {
      this.formData = Object.assign(this.formData, {
        id: '',
        eventTitle: '',
        eventTimeRange: [],
        projectManagerAuditor: '',
        eventDetail: '',
        feedbackList: []
      })
      this.roleIndicatorCache = {};
      this.primaryIndicatorOptions = {};
      this.secondaryIndicatorOptions = {};
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    /** 保存 */
    confirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = {...this.formData}
          params.eventStartTime = params.eventTimeRange[0] + ' 00:00:00'
          params.eventEndTime = params.eventTimeRange[1] + ' 23:59:59'
          delete params.eventTimeRange
          // 是否移动端提交
          params.isMobile = true

          this.btnLoading = true
          performanceEventAdd(params).then(res => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success(res.msg)
              this.cancel()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg)
            }
          }).catch(err => {
            this.btnLoading = false
          })
        }
      })
    },
    /** 新增反馈人员 */
    addRecommendPerson () {
      this.formData.feedbackList.push({
        nickName: '',
        primaryIndicator: '',
        secondaryIndicator: '',
        recommendedLevel: '',
        indicatorResultIds: [],
        recommendedReason: ''
      })
      this.$refs.form.validateField('feedbackList')
      this.$nextTick(() => {
        this.scrollToElement(`recommendDetail${this.formData.feedbackList.length}`)
      })
    },
    scrollToElement (refName) {
      const element = this.$refs[refName]
      if (element) {
        // 平滑滚动到元素位置
        element[0].$el.scrollIntoView({
          behavior: 'smooth',  // 平滑滚动
          block: 'start'       // 顶部对齐（可选：start/center/end）
        })
      }
    },
    /** 删除反馈人员 */
    removeRecommendPerson (index) {
      this.formData.feedbackList.splice(index, 1)
      this.$nextTick(() => {
        this.scrollToElement(`recommendDetail${this.formData.feedbackList.length}`)
      })
    },
    /** 一类指标、二类指标下拉数据 */
    async indicatorList(roleId, index) {
      // 如果缓存中已有该角色的指标数据，直接使用
      if (this.roleIndicatorCache[roleId]) {
        const { primaryOptions, secondaryOptions } = this.roleIndicatorCache[roleId];
        this.$set(this.primaryIndicatorOptions, index, primaryOptions);
        this.$set(this.secondaryIndicatorOptions, index, secondaryOptions);
        return;
      }
      try {
        const res = await getRoleIndicator(roleId);
        if (res.code === 200) {
          // 只保留 secondaryIndicators 至少有一个 systemGenerated 为 false 的一类指标
          const primaryOptions = res.data.filter(item =>
            Array.isArray(item.secondaryIndicators) &&
            item.secondaryIndicators.some(second => !second.systemGenerated)
          );

          let secondaryOptions = []; // 二类指标下拉数据
          primaryOptions.forEach(item => {
            if (Array.isArray(item.secondaryIndicators)) {
              secondaryOptions.push(...item.secondaryIndicators.map(second => {
                return {
                  code: second.code,
                  name: second.name,
                  primaryIndicator: item.code,
                  systemGenerated: second.systemGenerated
                }
              }));
            }
          });
          // 缓存角色指标数据
          this.$set(this.roleIndicatorCache, roleId, {
            primaryOptions,
            secondaryOptions
          });
          // 设置当前索引的指标选项
          this.$set(this.primaryIndicatorOptions, index, primaryOptions);
          this.$set(this.secondaryIndicatorOptions, index, secondaryOptions);
        } else {
          this.$message.error(res.msg);
          this.$set(this.primaryIndicatorOptions, index, []);
          this.$set(this.secondaryIndicatorOptions, index, []);
        }
      } catch (error) {
        console.error('获取指标列表失败:', error);
        this.$set(this.primaryIndicatorOptions, index, []);
        this.$set(this.secondaryIndicatorOptions, index, []);
      }
    },
    /** 一类指标变更 */
    changePrimaryIndicator (row) {
      this.$set(row, 'secondaryIndicator', '')
      this.$set(row, 'recommendedLevel', '')
      this.$set(row, 'indicatorResultIds', [])
    },
    /** 二类指标变更 */
    changeSecondaryIndicator (row) {
      this.$set(row, 'recommendedLevel', '')
      this.$set(row, 'indicatorResultIds', [])
    },
    /** 获取推荐理由下拉数据 */
    getIndicatorResultIdsOptions () {
      performIndicatorResultList({
        pageSize: 10000,
        pageNum: 1
      }).then(res => {
        if (res.code === 200) {
          this.indicatorResultIdsOptions = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 获取用户列表 */
    listUserAll () {
      listUserAll().then(res => {
        this.userOptions = res
      })
    },
    async changeUser(row, index) {
      // 清空当前行的指标选择
      this.$set(row, 'primaryIndicator', '');
      this.$set(row, 'secondaryIndicator', '');
      this.$set(row, 'recommendedLevel', '');
      this.$set(row, 'indicatorResultIds', []);

      if (!row.nickName) {
        this.$set(this.primaryIndicatorOptions, index, []);
        this.$set(this.secondaryIndicatorOptions, index, []);
        return;
      }

      // 获取选中用户的roleId
      const selectedUser = this.userOptions.find(user => user.nickName === row.nickName);
      if (selectedUser && selectedUser.roles && selectedUser.roles.length > 0) {
        // 使用第一个roleId
        const roleId = selectedUser.roles[0].roleId;
        await this.indicatorList(roleId, index);
      } else {
        // 如果没有roleId，清空指标选项
        this.$set(this.primaryIndicatorOptions, index, []);
        this.$set(this.secondaryIndicatorOptions, index, []);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.addEditFeedbackForm {
  max-width: 750px;
  margin: auto;
  padding: .2rem;
  padding-bottom: 60px;
  ::v-deep .el-card__body {
    padding: .3rem .4rem .5rem;
  }
  .basic-info {
    ::v-deep>.el-form-item__label {
      float: left;
      margin: 20px 0 10px 0;
      font-size: 18px;
      width: 100%;
      text-align: left;
    }
    .el-form-item {
      margin-top: .16rem;
    }
    .el-card {
      width: 7.1rem
    }
  }
  .introduction-item {
    .el-textarea {
      width: 6.2rem;
    }
  }
  .recommend-detail.el-form-item, .basic-info.el-form-item {
    margin-right: 0;
  }
  .recommend-detail {
    position: relative;
    width: 100%;
    .title {
      margin-top: -8px;
      margin-bottom: -8px;
      width: 80%;
    }
    ::v-deep>.el-form-item__label {
      float: left;
      margin: 20px 0 10px 0;
      padding: 0;
      font-size: 18px;
      width: 100%;
      text-align: left;
    }
    .el-card {
      width: 7.1rem
    }
    .el-form-item {
      margin-top: .16rem;
    }
    ::v-deep .el-form-item__content {
      width: 100%;
      .el-card {
        position: relative;
        margin-bottom: .2rem;
        .remove-person {
          position: absolute;
          right: .16rem;
          top: .16rem;
          cursor: pointer;
        }
        .el-form-item__content {
          width: fit-content;
          margin-right: .4rem;
        }
        .el-form-item__label {
          width: fit-content!important;
        }
        .subjective-reason {
          margin-top: .24rem;
          .el-textarea {
            width: 6.2rem;
          }
        }
      }
    }
    .van-field-nickName {
      padding: 0 15px;
      height: 36px;
      line-height: 36px;
      border: 1px solid #dfe4ed;
      border-radius: 4px;
      ::v-deep .van-cell__right-icon {
        position: absolute;
        height: 100%;
        right: 5px;
        padding-top: 5px;
        top: 0;
        color: #C0C4CC;
      }
    }
    ::v-deep .is-error .van-field-nickName {
      border-color: #ff4949;
    }
    .indicatorResultP {
      margin: 0;
      line-height: 22px;
      margin-bottom: 8px;
      width: 6.2rem;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-input__count {
    bottom: -.32rem;
    line-height: .24rem;
  }
  .btn-group {
    padding-top: 10px;
    padding-bottom: 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    background: #fff;
    width: 100%;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
@import '~vant/lib/index.css'; // 引入样式
/* 当视口宽度大于等于 750px 时应用 */
@media (min-width: 750px) {
  .van-popup {
    max-width: 750px;
    left: calc(50vw - 375px);
  }
}
.user-popup {
  .van-search {
    height: 50px;
  }
  .van-radio-group {
    height: calc(80vh - 100px);
    overflow: auto;
  }
  .confirm-button {
    position: absolute;
    bottom: 0;
    margin: 7px 16px;
    width: calc(100% - 34px);
    height: 36px;
  }
}
.template-list-popup {
  div {
    padding: 10px;
    font-size: 14px;
    &.title {
      line-height: 30px;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }
    &.content {
      padding: 0 10px 10px;
      height: calc(80vh - 100px);
      overflow: auto;
      div {
        margin-bottom: 18px;
      }
    }
  }
  .confirm-button {
    position: absolute;
    bottom: 0;
    margin: 7px 16px;
    width: calc(100% - 34px);
    height: 36px;
  }
}
</style>
<style>
.el-scrollbar {
  .el-scrollbar__bar {
    opacity: 1 !important;
  }
}
</style>
