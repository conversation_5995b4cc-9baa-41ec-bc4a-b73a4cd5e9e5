import request from '@/utils/request'
// 绩效反馈明细-分页查询
export function performanceFeedbackList(query) {
  return request({
    url: '/system/performanceFeedback/list',
    method: 'get',
    params: query
  })
}
// 绩效事件管理-列表
export function performanceEventList(query) {
  return request({
    url: '/system/performanceEvent/list',
    method: 'get',
    params: query
  })
}
// 绩效事件管理-获取详情
export function performanceEventDetail(params) {
  return request({
    url: '/system/performanceEvent/' + params.id,
    method: 'get'
  })
}
// 绩效事件管理-新增
export function performanceEventAdd(data) {
  return request({
    url: '/system/performanceEvent',
    method: 'post',
    data: data
  })
}
// 绩效事件管理-修改
export function performanceEventEdit(data) {
  return request({
    url: '/system/performanceEvent',
    method: 'put',
    data: data
  })
}
// 绩效事件管理-删除
export function performanceEventDelete(ids) {
  return request({
    url: '/system/performanceEvent/' + ids,
    method: 'delete'
  })
}
// 绩效事件管理-保存并提交
export function performanceEventSaveAndSubmit(data) {
  return request({
    url: '/system/performanceEvent/saveAndSubmit',
    method: 'post',
    data: data
  })
}
// 绩效反馈管理-列表
export function performanceFeedbackMainList(query) {
  return request({
    url: '/system/performanceFeedbackMain/list',
    method: 'get',
    params: query
  })
}
// 绩效反馈管理-新增
export function performanceFeedbackMainAdd(data) {
  return request({
    url: '/system/performanceFeedbackMain',
    method: 'post',
    data: data
  })
}
// 绩效反馈管理-修改
export function performanceFeedbackEdit(data) {
  return request({
    url: '/system/performanceFeedback',
    method: 'put',
    data: data
  })
}
// 绩效反馈管理-删除
export function performanceFeedbackMainDelete(ids) {
  return request({
    url: '/system/performanceFeedbackMain/' + ids,
    method: 'delete'
  })
}
// 绩效反馈管理-批量提交
export function feedbackBatchSubmit(data) {
  return request({
    url: '/system/performanceEvent/batchSubmit',
    method: 'post',
    data: data
  })
}
// 绩效反馈管理-项管审核
export function projectManagerAudit(data) {
  return request({
    url: '/system/performanceFeedbackMain/projectManagerAudit',
    method: 'post',
    data: data
  })
}
// 绩效反馈管理-最终审核
export function batchFinalAudit(data) {
  return request({
    url: '/system/performanceFeedbackMain/batchFinalAudit',
    method: 'post',
    data: data
  })
}
// 一类指标二类指标下拉数据
export function indicatorList(query) {
  return request({
    url: '/performIndicatorResult/indicatorList',
    method: 'get',
    params: query
  })
}
// 获取角色的一类二类指标下拉数据
export function getRoleIndicator(roleId) {
  return request({
    url: `/performIndicatorResult/getRoleIndicator/${roleId}`,
    method: 'get'
  })
}
// 查询绩效指标原因列表
export function performIndicatorResultList(query) {
  return request({
    url: '/performIndicatorResult/list',
    method: 'get',
    params: query
  })
}
// 绩效反馈明细-批量取消
export function feedbackBatchCancel(data) {
  return request({
    url: '/system/performanceFeedback/cancel',
    method: 'post',
    data: data
  })
}
