<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="queryParams" size="small" label-width="40px">
      <el-form-item label="年">
        <el-select v-model="queryParams.year" placeholder="请选择年份">
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year"/>
        </el-select>
      </el-form-item>
      <el-form-item label="月">
        <el-select v-model="queryParams.month" placeholder="请选择月份" clearable>
          <el-option v-for="month in monthOptions" :key="month" :label="month" :value="month" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-tag style="margin-left: 10px" type="info">实时统计</el-tag>
      </el-form-item>
    </el-form>

    <!-- 关键指标卡片 -->
    <div class="kpi-section">
      <div class="kpi-cards">
        <el-card class="kpi-card">
          <div class="kpi-title">待辅导总数</div>
          <div class="kpi-value">{{ statData.pendingTutoringCount || 0 }}</div>
        </el-card>
        <el-card class="kpi-card">
          <div class="kpi-title">已辅导总数</div>
          <div class="kpi-value">{{ statData.completedTutoringCount || 0 }}</div>
        </el-card>
        <el-card class="kpi-card">
          <div class="kpi-title">涉及员工总人数</div>
          <div class="kpi-value">{{ statData.involvedEmployeesCount || 0 }}</div>
        </el-card>
        <el-card class="kpi-card">
          <div class="kpi-title">C绩效总数</div>
          <div class="kpi-value">{{ statData.clevelCount || 0 }}</div>
        </el-card>
        <el-card class="kpi-card">
          <div class="kpi-title">D绩效总数</div>
          <div class="kpi-value">{{ statData.dlevelCount || 0 }}</div>
        </el-card>
      </div>
    </div>

    <el-row :gutter="20" style="margin-bottom: 20px">
      <!-- 辅导与绩效趋势图 -->
      <el-col :span="24" >
        <el-card >
          <div ref="trendChart" class="chart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

      <!-- 各组辅导次数分布 -->
      <el-row :gutter="20" style="margin-bottom: 20px">
        <el-col :span="24" >
          <el-card >
            <div ref="deptChart" class="chart" style="height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>

    <el-row :gutter="20" style="margin-bottom: 20px">
    <div class="charts-section">
      <!-- 各岗位辅导次数 -->
      <el-col :span="12" >
        <el-card >
          <div ref="postChart" class="chart" style="height: 400px;"></div>
        </el-card>
      </el-col>

      <!-- 员工个人辅导次数 -->
      <el-col :span="12" >
        <el-card >
          <div style="height: 400px;">
            <div style="font-size: 16px;font-weight: 600;">员工个人辅导次数</div>
            <div class="employee-list">
              <div class="list-header">
                <el-button-group>
                  <el-button :type="employeeListType === 'top5' ? 'primary' : ''"@click="employeeListType = 'top5'">TOP5
                  </el-button>
                  <el-button :type="employeeListType === 'bottom5' ? 'primary' : ''"@click="employeeListType = 'bottom5'">末位5</el-button>
                </el-button-group>
              </div>
              <div class="employee-list-content">
                <div v-for="(employee, index) in displayEmployeeList" :key="index" class="employee-item">
                  <div class="employee-rank">{{ employee.rank }}</div>
                  <div class="employee-info">
                    <div class="employee-name">{{ employee.nickName }}</div>
                  </div>
                  <div class="employee-count">{{ employee.tutoringCount }}次</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </div>
    </el-row>

    <!-- 辅导指标类型分布 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="24" >
        <el-card >
          <div ref="indicatorChart" class="chart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import * as echarts from 'echarts'
import { techCenterTutorStat } from '@/api/business/performanceGuidanceManagement'

export default {
  name: 'TechCenterTutorStat',
  data() {
    return {
      // 查询参数
      queryParams: {
        year: '',
        month: ''
      },
      yearOptions:[],
      monthOptions:[],
      // 统计数据
      statData: {
        pendingTutoringCount: 0,
        completedTutoringCount: 0,
        involvedEmployeesCount: 0,
        clevelCount: 0,
        dlevelCount: 0,
        tutorPerformanceTrendData: [],
        deptDistributionDataList: [],
        postDistributionDataList: [],
        memberTutoringDataList: [],
        indicatorTypeDataList: []
      },
      // 员工列表类型
      employeeListType: 'top5',
      // 图表实例
      trendChart: null,
      deptChart: null,
      postChart: null,
      indicatorChart: null
    }
  },
  computed: {
    displayEmployeeList() {
      if (!this.statData.memberTutoringDataList) return []
      const sortedList = [...this.statData.memberTutoringDataList].sort((a, b) =>
        this.employeeListType === 'top5' ? b.tutoringCount - a.tutoringCount : a.tutoringCount - b.tutoringCount
      )
      // 处理并列排名
      const result = []
      let currentRank = 1
      let currentCount = null

      for (let i = 0; i < sortedList.length; i++) {
        const item = sortedList[i]
        if (currentCount === null || item.tutoringCount !== currentCount) {
          currentRank = i + 1
          currentCount = item.tutoringCount
        }
        result.push({...item, rank: currentRank})
      }
      // 如果是TOP5模式，显示前5名以及所有与第5名并列的
      if (this.employeeListType === 'top5') {
        if (result.length <= 5) {
          return result
        }
        // 找到第5名的次数
        const fifthCount = result[4].tutoringCount
        // 返回所有次数大于等于第5名次数的员工
        return result.filter(item => item.tutoringCount >= fifthCount)
      } else {
        // 末位5模式，显示后5名以及所有与第5名并列的
        if (result.length <= 5) {
          return result
        }
        // 找到倒数第5名的次数
        const fifthFromLastCount = result[result.length - 5].tutoringCount
        // 返回所有次数小于等于倒数第5名次数的员工
        return result.filter(item => item.tutoringCount <= fifthFromLastCount)
      }
    }
  },
  async mounted() {
    await this.initYearMonthOptions()
    this.getList()
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.trendChart) this.trendChart.dispose()
    if (this.deptChart) this.deptChart.dispose()
    if (this.postChart) this.postChart.dispose()
    if (this.indicatorChart) this.indicatorChart.dispose()
  },
  methods: {
    /** 初始化年月选项 */
    initYearMonthOptions() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      for (let i = 0; i < 5; i++) {
        this.yearOptions.push(currentYear - i);
      }
      for (let i = 1; i <= 12; i++) {
        this.monthOptions.push(i);
      }
      this.queryParams.year = currentYear;
      this.queryParams.month = currentMonth;
    },
    // 获取统计数据
    async getList() {
      try {
        const response = await techCenterTutorStat(this.queryParams)
        if (response.code === 200) {
          this.statData = response.data
          this.$nextTick(() => {
            this.initCharts()
          })
        }
      } catch (error) {
        console.error(error)
      }
    },

    // 初始化图表
    initCharts() {
      this.initTrendChart()
      this.initDeptChart()
      this.initPostChart()
      this.initIndicatorChart()
    },

    // 辅导与绩效趋势图
    initTrendChart() {
      if (!this.$refs.trendChart) return
      this.trendChart = echarts.init(this.$refs.trendChart)
      const option = {
        title: {
          text: '辅导与绩效趋势',
          left: 'left'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: this.statData.tutorPerformanceTrendData?.map(item => item.statTypeName) || []
        },
        xAxis: {
          type: 'category',
          data: this.getTrendMonths()
        },
        yAxis: {
          type: 'value'
        },
        series: this.getTrendSeries()
      }
      this.trendChart.setOption(option)
    },

    // 初始化部门分布图
    initDeptChart() {
      if (!this.$refs.deptChart) return
      this.deptChart = echarts.init(this.$refs.deptChart)
      const option = {
        title: {
          text: '各组辅导次数分布',
          left: 'left'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.statData.deptDistributionDataList?.map(item => item.deptName) || []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: this.statData.deptDistributionDataList?.map(item => item.tutoringCount) || [],
          type: 'bar',
        }]
      }
      this.deptChart.setOption(option)
    },

    // 初始化岗位分布图
    initPostChart() {
      if (!this.$refs.postChart) return
      this.postChart = echarts.init(this.$refs.postChart)
      const option = {
        title: {
          text: '各岗位辅导次数',
          left: 'left'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          data: this.statData.postDistributionDataList?.map(item => item.postName) || []
        },
        series: [{
          name: '辅导次数',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          data: this.statData.postDistributionDataList?.map(item => ({
            name: item.postName,
            value: item.tutoringCount
          })) || [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      this.postChart.setOption(option)
    },

    // 初始化指标类型分布图
    initIndicatorChart() {
      if (!this.$refs.indicatorChart) return
      this.indicatorChart = echarts.init(this.$refs.indicatorChart)

      const option = {
        title: {
          text: '辅导指标类型分布',
          left: 'left'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.statData.indicatorTypeDataList?.map(item => item.indicatorTypeName) || [],
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: this.statData.indicatorTypeDataList?.map(item => item.count) || [],
          type: 'bar',
          itemStyle: {
            color: function(params) {
              const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#409EFF', '#67C23A']
              return colors[params.dataIndex % colors.length]
            }
          }
        }]
      }

      this.indicatorChart.setOption(option)
    },

    // 获取趋势图月份数据
    getTrendMonths() {
      if (this.statData.tutorPerformanceTrendData && this.statData.tutorPerformanceTrendData.length > 0) {
        return this.statData.tutorPerformanceTrendData[0].months || []
      }
      // 如果没有数据，返回默认月份
      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push(`${i}月`)
      }
      return months
    },

    // 获取趋势图系列数据
    getTrendSeries() {
      const series = []
      if (this.statData.tutorPerformanceTrendData && this.statData.tutorPerformanceTrendData.length > 0) {
        for (let i = 0; i < this.statData.tutorPerformanceTrendData.length; i++) {
          const item = this.statData.tutorPerformanceTrendData[i]
          series.push({
            name: item.statTypeName,
            type: 'line',
            data: item.statCounts || new Array(12).fill(0),
          })
        }
      }

      return series
    },
    // 查询
    handleQuery() {
      this.getList()
    },
    // 重置
    resetQuery() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      this.queryParams = {
        year: currentYear,
        month: currentMonth
      }
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">
  .kpi-section {
    margin-bottom: 20px;
    .kpi-cards {
      display: flex;
      gap: 20px;
      .kpi-card {
        flex: 1;
        padding: 10px;
        border-radius: 10px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .kpi-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 12px;
        }
        .kpi-value {
          font-size: 20px;
          font-weight: 600;
        }
      }
    }
  }
  .employee-list {
    .list-header {
      margin-bottom: 20px;
      text-align: right;
    }
    .employee-list-content {
      max-height: 300px;
      overflow-y: auto;   // 垂直滚动
      padding-right: 15px;

      .employee-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        &:last-child {
          border-bottom: none;
        }
        .employee-rank {
          width: 30px;
          height: 30px;
          background: #409EFF;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-right: 16px;
          flex-shrink: 0;
        }
        .employee-info {
          flex: 1;
          min-width: 0;    // 允许内容收缩
        }
      }
    }
  }
</style>
